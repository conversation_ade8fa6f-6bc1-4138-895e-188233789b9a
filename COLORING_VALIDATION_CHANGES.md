# Coloring Data Sequence Validation Changes

## Overview

This document describes the modifications made to the flag rules system for Coloring data to implement specific sequence validation criteria. The changes ensure that valid touch sequences are not flagged, while maintaining appropriate flagging for invalid sequences.

## Changes Made

### 1. New Validation Function

Added `validate_coloring_sequence_pattern()` function in `process_csv_data.py` that validates if a Coloring sequence follows the specific valid pattern:

**Valid Pattern Requirements:**
1. Sequence starts with "Began" touchPhase event
2. Sequence ends with "Ended" touchPhase event  
3. Between "Began" and "Ended", the sequence may contain:
   - Any number of "Moved" or "Stationary" events
   - At most one "Canceled" event
4. The sequence must follow this exact pattern: `"Began" → ("Moved"/"Stationary")* → (optional "Canceled") → "Ended"`
5. If a "Canceled" event is present, it must be the last event before "Ended"

### 2. Modified Flag Rules Logic

Updated the `apply_flag_rules()` function to:

- **For valid sequences**: Apply NO flags (empty flags list)
- **For invalid sequences**: Continue applying existing flag rules (missing_Began, missing_Ended, has_canceled, etc.)

### 3. Key Implementation Details

**Pattern Validation Logic:**
```python
def validate_coloring_sequence_pattern(sequence_data):
    # Sort by time to ensure proper order
    sequence_data = sequence_data.sort_values('time')
    touch_phases = sequence_data['touchPhase'].tolist()
    
    # Check start and end phases
    if touch_phases[0] != 'Began' or touch_phases[-1] != 'Ended':
        return False
    
    # Validate middle events
    middle_phases = touch_phases[1:-1]
    canceled_count = middle_phases.count('Canceled')
    
    # At most one Canceled, and it must be last if present
    if canceled_count > 1:
        return False
    
    if canceled_count == 1:
        canceled_index = middle_phases.index('Canceled')
        if canceled_index != len(middle_phases) - 1:
            return False
    
    # All middle events must be valid
    valid_middle_phases = {'Moved', 'Stationary', 'Canceled'}
    return all(phase in valid_middle_phases for phase in middle_phases)
```

**Integration with Flag Rules:**
```python
# Only apply flags if sequence doesn't follow valid pattern
if not is_valid_pattern:
    # Apply existing flag rules
    if (finger_id, seq_id) in missing_began_seqs:
        flags.append('missing_Began')
    # ... other flag checks
```

## Examples

### Valid Sequences (No Flags)

1. **Simple sequence**: `["Began", "Moved", "Ended"]`
2. **Multiple movements**: `["Began", "Moved", "Moved", "Stationary", "Ended"]`
3. **With canceled**: `["Began", "Moved", "Stationary", "Canceled", "Ended"]`
4. **Minimal sequence**: `["Began", "Ended"]`

### Invalid Sequences (Flagged)

1. **Missing Began**: `["Moved", "Ended"]` → gets `missing_Began` flag
2. **Missing Ended**: `["Began", "Moved"]` → gets `missing_Ended` flag  
3. **Multiple Canceled**: `["Began", "Canceled", "Canceled", "Ended"]` → gets `has_canceled` flag
4. **Canceled not at end**: `["Began", "Canceled", "Moved", "Ended"]` → gets appropriate flags

## Testing

Created comprehensive tests to verify the implementation:

1. **Unit Tests** (`test_coloring_validation.py`):
   - Tests the `validate_coloring_sequence_pattern()` function directly
   - Covers valid sequences, invalid sequences, and edge cases
   - All tests pass ✓

2. **Integration Tests** (`test_integration.py`):
   - Tests the complete flag rules system with the new validation
   - Verifies that valid sequences get no flags
   - Verifies that invalid sequences get appropriate flags
   - All tests pass ✓

## Impact

### Before Changes
- All sequences were evaluated against individual flag rules
- Valid sequences could receive flags like `has_canceled` even when following the correct pattern

### After Changes  
- Valid sequences following the specific pattern receive NO flags
- Invalid sequences continue to receive appropriate flags for debugging
- Maintains backward compatibility for Tracing data and other functionality

## Files Modified

1. **`process_csv_data.py`**:
   - Added `validate_coloring_sequence_pattern()` function
   - Modified `apply_flag_rules()` function to use pattern validation
   - Enhanced logging to indicate when enhanced validation is active

2. **Test Files Created**:
   - `test_coloring_validation.py` - Unit tests for validation function
   - `test_integration.py` - Integration tests for complete system

## Backward Compatibility

- No breaking changes to existing functionality
- Tracing data processing remains unchanged
- All existing flag types and logic preserved for invalid sequences
- Only affects Coloring data sequences that follow the valid pattern

## Usage

The changes are automatically applied when processing Coloring data. No additional configuration or parameters are required. The system will:

1. Detect Coloring data automatically
2. Apply the enhanced sequence validation
3. Only flag sequences that don't follow the valid pattern
4. Continue normal processing for all other cases
